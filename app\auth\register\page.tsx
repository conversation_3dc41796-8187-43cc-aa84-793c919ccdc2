'use client';

import { useEffect } from 'react';
import { RegisterForm } from '@/components/auth/register-form';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/store';
import { register } from '@/store/slices/authSlice';
import toast from 'react-hot-toast';
import type { AuthResponse } from '@/types/auth';
import Cookies from 'js-cookie';

export default function RegisterPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(state => state.auth.isLoading);

  useEffect(() => {
    // Only run on client
    if (typeof window !== 'undefined') {
      const roleId = localStorage.getItem('selectedRoleId');
      if (!roleId) {
        router.replace('/choose-role');
      }
    }
  }, [router]);

  const handleRegister = async (data: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
  }) => {
    try {
      const role = localStorage.getItem('selectedRoleId') || '';
      const resultAction = await dispatch(register({ ...data, role }));
      const result = resultAction.payload as AuthResponse;
      if (register.fulfilled.match(resultAction)) {
        toast.success('Registration successful!');
        if (result?.data?.access_token) {
          Cookies.set('authToken', result.data.access_token, {
            expires: 7,
            path: '/',
          });
        }

        setTimeout(() => {
          router.replace('/dashboard');
        }, 1500);
        console.log('Register success:', result);
      } else {
        throw result;
      }
    } catch (err: unknown) {
      let errorMsg = 'Registration failed.';
      if (typeof err === 'string') {
        errorMsg = err;
      } else if (err && typeof err === 'object') {
        errorMsg =
          (err as any)?.message || (err as any)?.data?.message || errorMsg;
      }
      toast.error(errorMsg);
      console.error('Register error:', err);
    }
  };

  const handleLinkedInSignUp = async () => {
    try {
      const clientId =
        process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID || 'LINKEDIN_CLIENT_ID';
      const redirectUri =
        process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI ||
        'http://localhost:3000/auth/linkedin/callback';
      const state = Math.random().toString(36).substring(2, 15); // random state for CSRF
      const scope = 'r_liteprofile r_emailaddress';
      const linkedinAuthUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&state=${state}&scope=${encodeURIComponent(scope)}`;
      console.log('LinkedIn Auth URL:', linkedinAuthUrl);
      console.log('LinkedIn OAuth Params:', { clientId, redirectUri, state, scope });
      window.location.href = linkedinAuthUrl;
    } catch (err) {
      console.error('Error in handleLinkedInSignUp:', err);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side */}
      <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-b from-[#1976F6] to-[#4F8DFD] rounded-tr-[40px] rounded-br-[40px] p-12 relative">
        <h1 className="text-white text-4xl font-extrabold mb-4 text-center drop-shadow-lg">
          Where Top Talents Meet Opportunities
        </h1>
        <p className="text-white text-lg text-center opacity-80 max-w-md">
          TalentLoop connects top candidates with leading employers using
          AI-powered matching. Discover opportunities, streamline hiring, and
          unlock your career potential—all in one platform.
        </p>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col min-h-screen bg-white">
        {/* Top Bar */}
        <div className="flex items-center justify-between px-10 pt-8">
          <Image
            src="/assets/logo/TalentLoop.svg"
            alt="TalentLoop Logo"
            width={140}
            height={36}
          />
          <nav className="flex gap-8 text-[#1976F6] font-medium text-base">
            <Link href="/">Home</Link>
            <a href="#">About us</a>
            <a href="#">Pricing</a>
            <a href="#">Contact Us</a>
          </nav>
        </div>
        {/* Main Content */}
        <div className="flex flex-1 flex-col justify-center items-center py-8">
          <RegisterForm
            onSubmit={handleRegister}
            onLinkedInSignUp={handleLinkedInSignUp}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
