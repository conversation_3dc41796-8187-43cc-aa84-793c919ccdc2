'use client';
import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAppDispatch } from '@/store';
import { linkedinSignup, linkedinSignin } from '@/store/slices/authSlice';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';

export default function LinkedInCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (error) {
      toast.error(error || 'LinkedIn login failed. Please try again.');
      return;
    }
    if (!code) {
      toast.error('No code found in URL. Please try logging in again.');
      return;
    }
    // Determine if this is signup or signin based on localStorage or route
    const isSignup = localStorage.getItem('selectedRoleId');
    if (isSignup) {
      const role = localStorage.getItem('selectedRoleId') || '';
      dispatch(linkedinSignup({ code, role }) as any)
        .unwrap()
        .then((result: any) => {
          if (result?.data?.access_token) {
            Cookies.set('authToken', result.data.access_token, {
              expires: 7,
              path: '/',
            });
          }
          toast.success('Sign in with LinkedIn successful! Redirecting...');
          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        })
        .catch((err: any) => {
          toast.error(err?.message || 'LinkedIn login failed."""');
        });
    } else {
      dispatch(linkedinSignin({ code }) as any)
        .unwrap()
        .then((result: any) => {
          if (result?.data?.access_token) {
            Cookies.set('authToken', result.data.access_token, {
              expires: 7,
              path: '/',
            });
          }
          toast.success('Sign in with LinkedIn successful! Redirecting...');
          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        })
        .catch((err: any) => {
          toast.error(err?.message || 'LinkedIn login failed.+++++');
        });
    }
  }, [code, error, dispatch, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <span>Processing LinkedIn login...</span>
    </div>
  );
}
